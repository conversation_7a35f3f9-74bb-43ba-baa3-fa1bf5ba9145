package com.patsnap.drafting.request.aispecification;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 保存解析图片结果请求DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-28
 */
@Data
@ApiModel("保存解析图片结果请求")
public class SaveParsedFiguresRequestDTO {

    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID", required = true, example = "task_123456")
    @JsonProperty("task_id")
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
}
