package com.patsnap.drafting.manager.aispecification;

import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.model.aispecification.DocumentParseFigureBO;
import com.patsnap.drafting.model.aispecification.FigureContentBO;
import com.patsnap.drafting.request.aispecification.Figure;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * SpecificationManager测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-28
 */
@ExtendWith(MockitoExtension.class)
class SpecificationManagerTest {

    @Mock
    private AiTaskManager aiTaskManager;

    @Mock
    private FileManager fileManager;

    @InjectMocks
    private SpecificationManager specificationManager;

    private String taskId;
    private DocumentParseFigureBO documentParseFigureBO;

    @BeforeEach
    void setUp() {
        taskId = "test-task-123";
        documentParseFigureBO = new DocumentParseFigureBO();
        documentParseFigureBO.setImageS3Keys(Arrays.asList(
                "folder/image1.png",
                "folder/image2.jpg",
                "folder/subfolder/image3.png"
        ));
    }

    @Test
    void testSaveParsedFigures_Success() {
        // Given
        when(aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.DOCUMENT_PARSE_FIGURES))
                .thenReturn(documentParseFigureBO);
        when(fileManager.signFile("folder/image1.png")).thenReturn("https://signed-url1.com");
        when(fileManager.signFile("folder/image2.jpg")).thenReturn("https://signed-url2.com");
        when(fileManager.signFile("folder/subfolder/image3.png")).thenReturn("https://signed-url3.com");

        // When
        FigureContentBO result = specificationManager.saveParsedFigures(taskId);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFigures());
        assertEquals(3, result.getFigures().size());

        List<Figure> figures = result.getFigures();
        
        // 验证第一个图片
        Figure figure1 = figures.get(0);
        assertEquals("parsed_figure_1", figure1.getId());
        assertEquals("image1.png", figure1.getName());
        assertEquals("folder/image1.png", figure1.getImage().getS3Key());
        assertEquals("https://signed-url1.com", figure1.getImage().getUrl());
        assertNotNull(figure1.getAnnotations());
        assertTrue(figure1.getAnnotations().isEmpty());

        // 验证第二个图片
        Figure figure2 = figures.get(1);
        assertEquals("parsed_figure_2", figure2.getId());
        assertEquals("image2.jpg", figure2.getName());
        assertEquals("folder/image2.jpg", figure2.getImage().getS3Key());
        assertEquals("https://signed-url2.com", figure2.getImage().getUrl());

        // 验证第三个图片
        Figure figure3 = figures.get(2);
        assertEquals("parsed_figure_3", figure3.getId());
        assertEquals("image3.png", figure3.getName());
        assertEquals("folder/subfolder/image3.png", figure3.getImage().getS3Key());
        assertEquals("https://signed-url3.com", figure3.getImage().getUrl());

        // 验证调用了权限检查
        verify(aiTaskManager).checkEditPermission(taskId);
        
        // 验证保存了内容
        verify(aiTaskManager).updateTaskContent(eq(taskId), eq(AiTaskContentTypeEnum.FIGURES), any(FigureContentBO.class));
    }

    @Test
    void testSaveParsedFigures_NoDocumentParseData() {
        // Given
        when(aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.DOCUMENT_PARSE_FIGURES))
                .thenReturn(null);

        // When & Then
        BizException exception = assertThrows(BizException.class, () -> {
            specificationManager.saveParsedFigures(taskId);
        });

        // 验证调用了权限检查
        verify(aiTaskManager).checkEditPermission(taskId);
        
        // 验证没有保存内容
        verify(aiTaskManager, never()).updateTaskContent(any(), any(), any());
    }

    @Test
    void testSaveParsedFigures_EmptyImageList() {
        // Given
        DocumentParseFigureBO emptyDocumentParseFigureBO = new DocumentParseFigureBO();
        emptyDocumentParseFigureBO.setImageS3Keys(Arrays.asList());
        
        when(aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.DOCUMENT_PARSE_FIGURES))
                .thenReturn(emptyDocumentParseFigureBO);

        // When & Then
        BizException exception = assertThrows(BizException.class, () -> {
            specificationManager.saveParsedFigures(taskId);
        });

        // 验证调用了权限检查
        verify(aiTaskManager).checkEditPermission(taskId);
        
        // 验证没有保存内容
        verify(aiTaskManager, never()).updateTaskContent(any(), any(), any());
    }
}
