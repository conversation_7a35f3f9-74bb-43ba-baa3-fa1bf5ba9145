package com.patsnap.drafting.manager.aispecification;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.patsnap.analytics.infrastructure.utils.TextUtils;
import com.patsnap.analytics.infrastructure.utils.highlight.HighlightOffset;
import com.patsnap.analytics.infrastructure.utils.highlight.HighlightParams;
import com.patsnap.analytics.infrastructure.utils.highlight.Tag;
import com.patsnap.common.jackson.JsonMapper;
import com.patsnap.common.request.RoleIdsHolder;
import com.patsnap.common.request.UserIdHolder;
import com.patsnap.core.common.AnalyticsCommonService;
import com.patsnap.core.common.InvokeBean;
import com.patsnap.core.common.acl.AclConstants;
import com.patsnap.core.common.acl.AclResourceConstants;
import com.patsnap.core.common.copilot.gpt.enums.GPTModelEnum;
import com.patsnap.core.spring.boot.autoconfigure.web.entity.CommonResponse;
import com.patsnap.drafting.annotation.CreditCheckLimit;
import com.patsnap.drafting.annotation.TaskContentCache;
import com.patsnap.drafting.client.ComputeClient;
import com.patsnap.drafting.client.OpenAiClient;
import com.patsnap.drafting.client.model.AiSpecificationComputeData;
import com.patsnap.drafting.client.model.AiSpecificationComputeReqDTO;
import com.patsnap.drafting.client.model.ComputeClaimFeature;
import com.patsnap.drafting.constants.Constant;
import com.patsnap.drafting.enums.aispecification.JurisdictionEnum;
import com.patsnap.drafting.enums.prompt.ClaimTypeEnum;
import com.patsnap.drafting.enums.prompt.ScenarioEnum;
import com.patsnap.drafting.enums.task.AiTaskContentTypeEnum;
import com.patsnap.drafting.enums.task.AiTaskTypeEnum;
import com.patsnap.drafting.exception.BizException;
import com.patsnap.drafting.exception.errorcode.ContentErrorCodeEnum;
import com.patsnap.drafting.exception.errorcode.TaskErrorCodeEnum;
import com.patsnap.drafting.manager.FileManager;
import com.patsnap.drafting.manager.acl.AclCheckManager;
import com.patsnap.drafting.manager.acl.LimitCheckManager;
import com.patsnap.drafting.manager.aitask.AiTaskManager;
import com.patsnap.drafting.manager.imagesearch.ImageUploadManager;
import com.patsnap.drafting.manager.patent.ClassificationManager;
import com.patsnap.drafting.model.aispecification.ClaimFeature;
import com.patsnap.drafting.model.aispecification.ClaimFeatureContentBO;
import com.patsnap.drafting.model.aispecification.ClaimParserDetail;
import com.patsnap.drafting.model.aispecification.ClaimParserIdx;
import com.patsnap.drafting.model.aispecification.ClaimParserRequest;
import com.patsnap.drafting.model.aispecification.ClaimParserResult;
import com.patsnap.drafting.model.aispecification.ClaimSegments;
import com.patsnap.drafting.model.aispecification.FeatureTreeBO;
import com.patsnap.drafting.model.aispecification.FeatureTreeSimpleBO;
import com.patsnap.drafting.model.aispecification.FigureContentBO;
import com.patsnap.drafting.model.aispecification.SpecificationDisclosureExtractEmbodimentBO;
import com.patsnap.drafting.model.aispecification.SpecificationEmbodimentGenerateBO;
import com.patsnap.drafting.model.aispecification.SpecificationEmbodimentOutlineBO;
import com.patsnap.drafting.model.aispecification.SpecificationInitializationBO;
import com.patsnap.drafting.model.aispecification.SpecificationTechWrapperBO;
import com.patsnap.drafting.model.aispecification.TermContentBO;
import com.patsnap.drafting.request.aispecification.ClassificationHelperResponse;
import com.patsnap.drafting.request.aispecification.ClassificationSubClassResponse;
import com.patsnap.drafting.request.aispecification.ContentModifyReqDTO;
import com.patsnap.drafting.request.aispecification.DisclosureEmbodimentItem;
import com.patsnap.drafting.request.aispecification.EmbodimentGenerateItem;
import com.patsnap.drafting.request.aispecification.EmbodimentKeyWordReqDTO;
import com.patsnap.drafting.request.aispecification.EmbodimentKeyWordResDTO;
import com.patsnap.drafting.request.aispecification.EmbodimentOutlineModifyReqDTO;
import com.patsnap.drafting.request.aispecification.Figure;
import com.patsnap.drafting.request.aispecification.InputContentResDTO;
import com.patsnap.drafting.request.aispecification.SingleEmbodimentModifyReqDTO;
import com.patsnap.drafting.request.aispecification.SpecificationContentReqDTO;
import com.patsnap.drafting.request.aispecification.SpecificationInitDTO;
import com.patsnap.drafting.request.aispecification.Term;
import com.patsnap.drafting.request.aitask.AiTaskCreateReqDTO;
import com.patsnap.drafting.request.aitask.AiTaskReqDTO;
import com.patsnap.drafting.request.imagesearch.ImageUploadRequestDTO;
import com.patsnap.drafting.response.aispecification.SpecificationLangCheckResDTO;
import com.patsnap.drafting.response.aispecification.SpecificationRdResDTO;
import com.patsnap.drafting.response.imagesearch.ImageUploadResponseDTO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.patsnap.drafting.enums.prompt.SpecificationPromptKeyEnum.KEYWORD_EXPAND;
import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.FEATURE_TREE;
import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.FIGURES_PRE_INFO;
import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.FIGURE_CONFIG;
import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.FIGURE_CONFIRM;
import static com.patsnap.drafting.enums.task.AiTaskContentTypeEnum.TERMS;

@Service
@Slf4j
public class SpecificationManager {

    @Autowired
    private ComputeClient computeClient;

    @Autowired
    private AiTaskManager aiTaskManager;

    @Autowired
    private AnalyticsCommonService analyticsCommonService;

    @Value("${com.patsnap.analytics.service.analytics-search-url}")
    private String analyticsSearchUrl;

    @Value("${configs.com.patsnap.claimparser.service.url}")
    private String claimParserServiceUrl;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ClaimParseManager claimParseManager;

    @Autowired
    private OpenAiClient openAiClient;

    @Autowired
    private ClassificationManager classificationManager;

    @Autowired
    private LimitCheckManager limitCheckManager;

    @Autowired
    private AclCheckManager aclCheckManager;

    @Autowired
    private ImageUploadManager imageUploadManager;
    @Autowired
    private FileManager fileManager;

    @Autowired
    private AlgorithmAsyncProcessor algorithmAsyncProcessor;


    private static final JsonMapper mapper = JsonMapper.defaultMapper();
    private static final String DEP_CLM_CSS_CLASS = "dep-clm";
    private static final String INDEP_CLM_CSS_CLASS = "indep-clm";
    private static final String CLM_REFI_CSS_CLASS = "clm-refi";

    //支持的语言列表
    public static final List<String> SUPPORT_LANG_LIST = Lists.newArrayList(Constant.CN, Constant.EN);
    // 语言检测截取的最大字符数
    private static final int MAX_LANG_DETECT_LENGTH = 1000;


    /**
     * 1.检测用户输入 交底书和权利要求的语言是否一致，与选择的受理局是否一致
     *
     * @param specificationInitDTO init请求
     * @return init结果
     */
    public SpecificationLangCheckResDTO langDetect(SpecificationInitDTO specificationInitDTO) {
        long startTime = System.currentTimeMillis();
        //检测语言（只取前1000字符提高性能）
        String disclosure = StringUtils.isBlank(specificationInitDTO.getDisclosure()) ?
                StringUtils.EMPTY : StringUtils.substring(specificationInitDTO.getDisclosure(), 0, MAX_LANG_DETECT_LENGTH);
        String claim = StringUtils.isBlank(specificationInitDTO.getClaim()) ?
                StringUtils.EMPTY : StringUtils.substring(specificationInitDTO.getClaim(), 0, MAX_LANG_DETECT_LENGTH);

        log.info("截取后的交底书文本长度：{}，权利要求文本长度：{}",
                StringUtils.length(disclosure), StringUtils.length(claim));

        String disclosureLang = computeClient.getCheckLang(disclosure);
        String claimLang = computeClient.getCheckLang(claim);

        //不是支持的语言
        if (!(SUPPORT_LANG_LIST.contains(disclosureLang) && SUPPORT_LANG_LIST.contains(claimLang))) {
            return SpecificationLangCheckResDTO.instanceOfUnspported();
        }
        //语言不一致
        if (!StringUtils.equals(disclosureLang, claimLang)) {
            return SpecificationLangCheckResDTO.instanceOfModify();
        }
        //语言一致,但是和受理局不一致，推荐受理局
        JurisdictionEnum jurisdictionEnum = JurisdictionEnum.fromName(specificationInitDTO.getJurisdiction());
        if (!StringUtils.equals(jurisdictionEnum.getValue(), claimLang)) {
            String suggestion = JurisdictionEnum.fromLang(claimLang).name();
            return SpecificationLangCheckResDTO.instanceOfSuggest(suggestion);
        }

        SpecificationLangCheckResDTO result = SpecificationLangCheckResDTO.instanceOfSuccess();
        log.info("语言检测完成，总用时：{}ms，交底书语言：{}，权利要求语言：{}",
                System.currentTimeMillis() - startTime, disclosureLang, claimLang);
        return result;
    }

    /**
     * 任务初始化,保存任务数据
     *
     * @param specificationInitDTO init请求对象
     */

    public void taskInit(SpecificationInitDTO specificationInitDTO) {
        //保存任务开始数据
        AiTaskCreateReqDTO aiTaskCreateReqDTO = new AiTaskCreateReqDTO();
        aiTaskCreateReqDTO.setTaskId(specificationInitDTO.getTaskId());
        aiTaskCreateReqDTO.setType(AiTaskTypeEnum.AI_SPECIFICATION.getType());
        aiTaskCreateReqDTO.setContent(Map.of(AiTaskContentTypeEnum.INITIALIZATION.getType(), mapper.toJson(
                new SpecificationInitializationBO(specificationInitDTO.getClaim(), specificationInitDTO.getDisclosure(),
                        specificationInitDTO.getJurisdiction()))));

        // 敏感词校验
        aiTaskManager.checkSensitiveWords(aiTaskCreateReqDTO.getContent().values().stream().map(Object::toString).toList());

        // 创建任务之前先校验次数和权限
        checkPermissionAndLimit(specificationInitDTO.getJurisdiction());

        // 敏感词检查和创建任务
        aiTaskManager.createTask(aiTaskCreateReqDTO);
    }

    private void checkPermissionAndLimit(String jurisdiction) {
        // 校验权限(CNIPA,USPTO,EPO,UNIFIED)
        List<AclConstants.AclCheckObject> resourcesList = List.of(AclResourceConstants.DRAFTING_AI_SPECIFICATION_CN,
                AclResourceConstants.DRAFTING_AI_SPECIFICATION_US,AclResourceConstants.DRAFTING_AI_SPECIFICATION_EU,
                AclResourceConstants.DRAFTING_AI_SPECIFICATION_UNIFIED);
        Map<String, Boolean> aclMap = aclCheckManager.batchCheck(RoleIdsHolder.get(), resourcesList);
        boolean cnPermission = aclMap.get(AclResourceConstants.DRAFTING_AI_SPECIFICATION_CN.getResourceName());
        boolean usPermission = aclMap.get(AclResourceConstants.DRAFTING_AI_SPECIFICATION_US.getResourceName());
        boolean euPermission = aclMap.get(AclResourceConstants.DRAFTING_AI_SPECIFICATION_EU.getResourceName());
        boolean unifiedPermission = aclMap.get(AclResourceConstants.DRAFTING_AI_SPECIFICATION_UNIFIED.getResourceName());
        
        // 权限校验：如果有统一权限或者各自的权限，都算OK，反之才抛出异常
        if(JurisdictionEnum.CNIPA.name().equalsIgnoreCase(jurisdiction) && !cnPermission && !unifiedPermission) {
            throw new BizException(TaskErrorCodeEnum.TASK_NO_PERMISSION_ACCESS);
        }
        if(JurisdictionEnum.USPTO.name().equalsIgnoreCase(jurisdiction) && !usPermission && !unifiedPermission) {
            throw new BizException(TaskErrorCodeEnum.TASK_NO_PERMISSION_ACCESS);
        }
        if(JurisdictionEnum.EPO.name().equalsIgnoreCase(jurisdiction) && !euPermission && !unifiedPermission) {
            throw new BizException(TaskErrorCodeEnum.TASK_NO_PERMISSION_ACCESS);
        }

        // 校验次数：优先扣除独立权限，然后才是扣除统一容量
        if (JurisdictionEnum.CNIPA.name().equalsIgnoreCase(jurisdiction)) {
            if (cnPermission) {
                limitCheckManager.checkLimit(UserIdHolder.get(), 1, Constant.AI_SPECIFICATION_CN_LIMIT);
            } else if (unifiedPermission) {
                limitCheckManager.checkLimit(UserIdHolder.get(), 1, Constant.AI_SPECIFICATION_UNIFIED_LIMIT);
            }
        }
        if (JurisdictionEnum.USPTO.name().equalsIgnoreCase(jurisdiction)) {
            if (usPermission) {
                limitCheckManager.checkLimit(UserIdHolder.get(), 1, Constant.AI_SPECIFICATION_US_LIMIT);
            } else if (unifiedPermission) {
                limitCheckManager.checkLimit(UserIdHolder.get(), 1, Constant.AI_SPECIFICATION_UNIFIED_LIMIT);
            }
        }
        if (JurisdictionEnum.EPO.name().equalsIgnoreCase(jurisdiction)) {
            if (euPermission) {
                limitCheckManager.checkLimit(UserIdHolder.get(), 1, Constant.AI_SPECIFICATION_EU_LIMIT);
            } else if (unifiedPermission) {
                limitCheckManager.checkLimit(UserIdHolder.get(), 1, Constant.AI_SPECIFICATION_UNIFIED_LIMIT);
            }
        }
    }

    /**
     * 获取用户输入内容
     *
     * @param specificationContentReqDTO 请求对象
     * @return 用户输入的内容
     */
    public InputContentResDTO getInputContent(SpecificationContentReqDTO specificationContentReqDTO) {
        SpecificationInitializationBO taskContent = aiTaskManager.getTaskContent(specificationContentReqDTO.getTaskId(),
                AiTaskContentTypeEnum.INITIALIZATION);
        return new InputContentResDTO(taskContent.getClaim(), taskContent.getDisclosure(),
                taskContent.getJurisdiction());
    }

    /**
     * 获取分类号，USPTO下获取CPC分类号，CNIPA下获取IPC分类号
     *
     * @param specificationContentReqDTO 分类号请求对象
     * @return 分类号列表
     */
    @TaskContentCache(contentType = AiTaskContentTypeEnum.CLASSIFICATION)
    public List<String> getClassification(SpecificationContentReqDTO specificationContentReqDTO) {
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(
                specificationContentReqDTO.getTaskId(), AiTaskContentTypeEnum.INITIALIZATION);
        String jurisdiction = initializationBO.getJurisdiction();
        List<String> classification;
        if (StringUtils.equals(JurisdictionEnum.CNIPA.name(), jurisdiction)) {
            classification = computeClient.getIpcListByDoc(StringUtils.EMPTY, StringUtils.EMPTY,
                    initializationBO.getClaim());
        } else {
            classification = computeClient.getCpcListByDoc(StringUtils.EMPTY, StringUtils.EMPTY,
                    initializationBO.getClaim());
        }
        return classification;
    }

    public CommonResponse<ClassificationSubClassResponse> helperSection(String type) {
        InvokeBean<CommonResponse<ClassificationSubClassResponse>> invokeBean = new InvokeBean<>();
        invokeBean.setHttpMethod(HttpMethod.GET);
        invokeBean.setUrl(analyticsSearchUrl + "/classification/helper-section?type={type}");
        invokeBean.setUriVariables(type);
        invokeBean.setResponseType(new ParameterizedTypeReference<>() {
        });
        return analyticsCommonService.invokeMethod(invokeBean);
    }


    /**
     * 透传调用到s-analytics-search服务的helper-subclass接口
     *
     * @param type  ipc/cpc
     * @param code  分类好code
     * @param level 层级
     * @param start 分页开始offset
     * @return 分类号明细
     */
    public CommonResponse<ClassificationSubClassResponse> helperSubClass(String type, String code, int level,
            Integer start) {
        InvokeBean<CommonResponse<ClassificationSubClassResponse>> invokeBean = new InvokeBean<>();
        invokeBean.setHttpMethod(HttpMethod.GET);
        invokeBean.setUrl(analyticsSearchUrl
                + "/classification/helper-subclass?type={type}&code={code}&start={start}&level={level}");
        invokeBean.setUriVariables(type, code, start, level);
        invokeBean.setResponseType(new ParameterizedTypeReference<>() {
        });
        return analyticsCommonService.invokeMethod(invokeBean);
    }

    /**
     * 透传调用到s-analytics-search服务的helper-search接口
     *
     * @param type  ipc/cpc
     * @param query 分类好code
     * @param start 分页开始offset
     * @return 分类号提示
     */
    public CommonResponse<ClassificationHelperResponse> helperSearch(String type, String query, int start) {
        InvokeBean<CommonResponse<ClassificationHelperResponse>> invokeBean = new InvokeBean<>();
        invokeBean.setHttpMethod(HttpMethod.GET);
        invokeBean.setUrl(analyticsSearchUrl + "/classification/helper-search?type={type}&q={q}&start={start}");
        invokeBean.setUriVariables(type, query, start);
        invokeBean.setResponseType(new ParameterizedTypeReference<>() {
        });
        return analyticsCommonService.invokeMethod(invokeBean);
    }

    /**
     * 修改AI说明书相关内容，包括分类号、类别、技术三要素、特征树、实施例、附图信息、术语信息等
     *
     * @param contentModifyReqDTO 内容修改请求DTO，包含需要修改的各种内容信息
     */
    public void contentModify(ContentModifyReqDTO contentModifyReqDTO) {
        aiTaskManager.checkEditPermission(contentModifyReqDTO.getTaskId());
        //更新分类号
        Optional.ofNullable(contentModifyReqDTO.getClassification())
                .ifPresent(classification -> aiTaskManager.updateTaskContent(contentModifyReqDTO.getTaskId(),
                        AiTaskContentTypeEnum.CLASSIFICATION,
                        mapper.toJson(classification)));
        //更新特征树
        Optional.ofNullable(contentModifyReqDTO.getFeatureTree()).ifPresent(
                featureTree -> aiTaskManager.updateTaskContent(contentModifyReqDTO.getTaskId(),
                        FEATURE_TREE, mapper.toJson(Map.of("feature_tree", featureTree))));

        //更新类型
        Optional.ofNullable(contentModifyReqDTO.getCategory())
                .ifPresent(category -> aiTaskManager.updateTaskContent(contentModifyReqDTO.getTaskId(),
                        AiTaskContentTypeEnum.CATEGORY, category));

        //更新技术三要素
        String technicalProblem = contentModifyReqDTO.getTechnicalProblem();
        String technicalMethods = contentModifyReqDTO.getTechnicalMethods();
        String benefit = contentModifyReqDTO.getBenefit();
        if (StringUtils.isNoneBlank(technicalProblem, technicalMethods, benefit)) {
            SpecificationTechWrapperBO techWrapperBO = new SpecificationTechWrapperBO();
            techWrapperBO.setTechnicalProblem(technicalProblem);
            techWrapperBO.setTechnicalMethods(technicalMethods);
            techWrapperBO.setBenefit(benefit);
            aiTaskManager.updateTaskContent(contentModifyReqDTO.getTaskId(), AiTaskContentTypeEnum.TECH_WRAPPER,
                    mapper.toJson(techWrapperBO));
        }

        // 更新交底书实施例
        if (CollectionUtils.isNotEmpty(contentModifyReqDTO.getEmbodiments())) {
            SpecificationDisclosureExtractEmbodimentBO disclosureEmbodimentBO = new SpecificationDisclosureExtractEmbodimentBO();
            disclosureEmbodimentBO.setEmbodiments(contentModifyReqDTO.getEmbodiments());
            aiTaskManager.updateTaskContent(contentModifyReqDTO.getTaskId(), AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_EXTRACT, mapper.toJson(disclosureEmbodimentBO));
        }

        // 更新附图信息
        saveFigure(contentModifyReqDTO);

        // 更新实施例大纲信息
        saveEmbodimentOutline(contentModifyReqDTO);

        // 更新术语信息
        saveTerms(contentModifyReqDTO);
    }

    /**
     * 保存术语信息
     */
    private void saveTerms(ContentModifyReqDTO contentModifyReqDTO) {
        Optional.ofNullable(contentModifyReqDTO.getTerms()).ifPresent(terms -> {
            log.info("更新术语信息，任务ID: {}", contentModifyReqDTO.getTaskId());
            try {
                // 校验 terms 中 unique_id 是否为空，为空的话,报错
                terms.forEach(term -> {
                    if (StringUtils.isBlank(term.getUniqueId())) {
                        log.error("术语信息中 unique_id 不能为空，任务ID: {}, 术语: {}", contentModifyReqDTO.getTaskId(), term);
                        throw new BizException(ContentErrorCodeEnum.SAVE_CONTENT_FAILED);
                    }
                });
                // 将Object转换为TermContentBO
                TermContentBO termContentBO = new TermContentBO();
                termContentBO.setTerms(terms);
                aiTaskManager.updateTaskContent(contentModifyReqDTO.getTaskId(), TERMS, termContentBO);
                log.info("术语信息更新成功，任务ID: {}, 术语数量: {}", contentModifyReqDTO.getTaskId(),
                        termContentBO.getTerms() != null ? termContentBO.getTerms().size() : 0);
            } catch (Exception e) {
                log.error("更新术语信息失败，任务ID: {}", contentModifyReqDTO.getTaskId(), e);
                throw new BizException(ContentErrorCodeEnum.SAVE_CONTENT_FAILED);
            }
        });
    }

    private void saveFigure(ContentModifyReqDTO contentModifyReqDTO) {
        Optional.ofNullable(contentModifyReqDTO.getFigures()).ifPresent(figures -> {
            log.info("更新附图信息，任务ID: {}", contentModifyReqDTO.getTaskId());
            try {
                // 将figures转换为FigureBOList
                FigureContentBO figureContentBO = new FigureContentBO();
                figureContentBO.setFigures(figures);
                aiTaskManager.updateTaskContent(contentModifyReqDTO.getTaskId(), FIGURE_CONFIG, figureContentBO);
                aiTaskManager.updateTaskContent(contentModifyReqDTO.getTaskId(), FIGURE_CONFIRM, "DONE");
                log.info("附图信息更新成功，任务ID: {}, 附图数量: {}", contentModifyReqDTO.getTaskId(),
                        figureContentBO.getFigures() != null ? figureContentBO.getFigures().size() : 0);

                // 异步调用算法接口处理附图,在获取附图说明时，获取任务结果
                algorithmAsyncProcessor.processAlgorithmAsync(contentModifyReqDTO.getTaskId(), FIGURES_PRE_INFO);
            } catch (Exception e) {
                log.error("更新附图信息失败，任务ID: {}", contentModifyReqDTO.getTaskId(), e);
                throw new BizException(ContentErrorCodeEnum.SAVE_CONTENT_FAILED);
            }
        });
        aiTaskManager.deleteTaskContentByContentType(contentModifyReqDTO.getTaskId(), List.of(FIGURES_PRE_INFO.getType()));
    }

    public void saveEmbodimentOutline(ContentModifyReqDTO contentModifyReqDTO) {
        if (CollectionUtils.isNotEmpty(contentModifyReqDTO.getOutlines())) {
            SpecificationEmbodimentOutlineBO specificationEmbodimentOutlineBO = new SpecificationEmbodimentOutlineBO();
            specificationEmbodimentOutlineBO.setOutlines(contentModifyReqDTO.getOutlines());
            aiTaskManager.updateTaskContent(contentModifyReqDTO.getTaskId(), AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_OUTLINE, mapper.toJson(specificationEmbodimentOutlineBO));
        }
    }

    @TaskContentCache(contentType = AiTaskContentTypeEnum.CLAIM_FORMAT)
    public String claimFormat(SpecificationContentReqDTO specificationContentReqDTO) {
        SpecificationInitializationBO initializationBO = getInitializationData(specificationContentReqDTO);
        return claimFormat(initializationBO);
    }


    public String claimFormat(SpecificationInitializationBO initializationBO) {
        ClaimParserResult claimParserResult = parseClaims(initializationBO);
        HighlightParams highlightParams = createHighlightParams(initializationBO.getClaim());
        processAllClaims(claimParserResult, highlightParams.getOffsets());
        return TextUtils.processText(highlightParams);
    }

    @CreditCheckLimit(contentType = AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_KEYWORD_EXPAND)
    public EmbodimentKeyWordResDTO embodimentKeyword(EmbodimentKeyWordReqDTO embodimentKeyWordReqDTO) {
        String prompt = getEmbodimentKeywordsPrompt(embodimentKeyWordReqDTO);
        //独权不用扩展上位词，目前再prompt里要求，代码里暂时没有控制
        return openAiClient.callGptByPrompt(GPTModelEnum.GPT_CLAUDE_3_7_SONNET, prompt, ScenarioEnum.AI_SPECIFICATION,
                new TypeReference<>() {
                });
    }

    /**
     * 生成实施例大纲
     *
     * @param aiTaskReqDTO 请求参数
     * @return 实施例大纲结果
     */
    public SpecificationEmbodimentOutlineBO generateEmbodimentOutline(AiTaskReqDTO aiTaskReqDTO) {
        // 获取任务内容
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(aiTaskReqDTO.getTaskId(), AiTaskContentTypeEnum.INITIALIZATION);

        // 检查权利要求是否存在
        if (StringUtils.isBlank(initializationBO.getClaim())) {
            throw new BizException(ContentErrorCodeEnum.CONTENT_EMPTY);
        }

        // 构建计算数据
        AiSpecificationComputeData computeData = new AiSpecificationComputeData();
        computeData.setClaimText(initializationBO.getClaim());
        String jurisdiction = initializationBO.getJurisdiction();
        String lang = StringUtil.toLowerCase(JurisdictionEnum.fromName(jurisdiction).getValue());

        // 调用算法服务生成实施例大纲
        AiSpecificationComputeReqDTO params = AiSpecificationComputeReqDTO.builder()
                .function("generate_embodiment_outline")
                .data(computeData)
                .lang(lang)
                .build();
        SpecificationRdResDTO specificationRdResDTO = computeClient.getSpecificationPrompt(params);

        // 将返回的实施例大纲列表转换为 BO 对象
        SpecificationEmbodimentOutlineBO result = new SpecificationEmbodimentOutlineBO();
        result.setOutlines(specificationRdResDTO.getOutline());
        return result;
    }

    // 新增或者编辑某个实施例大纲信息
    public SpecificationEmbodimentOutlineBO modifyEmbodimentOutline(EmbodimentOutlineModifyReqDTO embodimentOutlineModifyReqDTO) {
        aiTaskManager.checkEditPermission(embodimentOutlineModifyReqDTO.getTaskId());
        // 获取任务内容
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(embodimentOutlineModifyReqDTO.getTaskId(), AiTaskContentTypeEnum.INITIALIZATION);

        // 检查权利要求是否存在
        if (StringUtils.isBlank(embodimentOutlineModifyReqDTO.getClaimText())) {
            throw new BizException(ContentErrorCodeEnum.CONTENT_EMPTY);
        }

        // 构建计算数据
        AiSpecificationComputeData computeData = new AiSpecificationComputeData();
        computeData.setClaimText(embodimentOutlineModifyReqDTO.getClaimText());
        String jurisdiction = initializationBO.getJurisdiction();
        String lang = StringUtil.toLowerCase(JurisdictionEnum.fromName(jurisdiction).getValue());

        // 调用算法服务生成实施例大纲
        AiSpecificationComputeReqDTO params = AiSpecificationComputeReqDTO.builder()
                .function("generate_embodiment_outline_independent")
                .data(computeData)
                .lang(lang)
                .build();
        SpecificationRdResDTO specificationRdResDTO = computeClient.getSpecificationPrompt(params);

        // 将返回的实施例大纲列表转换为 BO 对象
        SpecificationEmbodimentOutlineBO result = new SpecificationEmbodimentOutlineBO();
        if (CollectionUtils.isNotEmpty(specificationRdResDTO.getOutline())) {
            specificationRdResDTO.getOutline().get(0).setClaimNumbers(embodimentOutlineModifyReqDTO.getClaimNumbers());
            result.setOutlines(specificationRdResDTO.getOutline());
        }
        return result;
    }


    /**
     * 获取通用的RD撰写服务的请求对象
     *
     * @param taskId 说明书撰写的任务Id
     * @return 请求compute服务的对象
     */
    public AiSpecificationComputeData getCommonComputeReqData(String taskId) {
        AiSpecificationComputeData aiSpecificationComputeData = claimParseManager.parseSimpleFormattedClmByTaskId(
                taskId);
        if (aiSpecificationComputeData == null) {
            throw new BizException(ContentErrorCodeEnum.CONTENT_EMPTY);
        }
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(taskId,
                AiTaskContentTypeEnum.INITIALIZATION);
        String jurisdiction = initializationBO.getJurisdiction();
        String disclosure = initializationBO.getDisclosure();
        List<String> classification = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.CLASSIFICATION);
        String category = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.CATEGORY);
        SpecificationTechWrapperBO specificationTechWrapperBO = aiTaskManager.getTaskContent(taskId,
                AiTaskContentTypeEnum.TECH_WRAPPER);
        ClaimFeatureContentBO claimFeatureContentBO = aiTaskManager.getTaskContent(taskId,
                FEATURE_TREE);

        // 查新和设置交底书实施例数据(用户选中的)，传递给算法接口，参与报告生成
        SpecificationDisclosureExtractEmbodimentBO disclosureEmbodimentBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_EXTRACT);
        if (Objects.nonNull(disclosureEmbodimentBO) && CollectionUtils.isNotEmpty(disclosureEmbodimentBO.getEmbodiments())) {
            List<DisclosureEmbodimentItem> disclosureEmbodiments = disclosureEmbodimentBO.getEmbodiments().stream().filter(DisclosureEmbodimentItem::getSelected).toList();
            if (CollectionUtils.isNotEmpty(disclosureEmbodiments)) {
                // 内部的number字段还需要设置一下,循环遍历设置，就取集合下标即可
                for (int i = 0; i < disclosureEmbodiments.size(); i++) {
                    disclosureEmbodiments.get(i).setNumber(i + 1);
                }
                aiSpecificationComputeData.setDisclosureEmbodiments(disclosureEmbodiments);
            }
        }

        String figuresPreInfo = aiTaskManager.getTaskContent(taskId, FIGURES_PRE_INFO);
        aiSpecificationComputeData.setDrawingNarrative(figuresPreInfo);
        aiSpecificationComputeData.setPatentType(category);
        aiSpecificationComputeData.setJurisdiction(initializationBO.getJurisdiction());
        aiSpecificationComputeData.setPatentOffice(initializationBO.getJurisdiction().toLowerCase());
        aiSpecificationComputeData.setDisclosureProblem(specificationTechWrapperBO.getTechnicalProblem());
        aiSpecificationComputeData.setDisclosureSolution(specificationTechWrapperBO.getTechnicalMethods());
        aiSpecificationComputeData.setDisclosureEfficacy(specificationTechWrapperBO.getBenefit());
        setClassificationInfo(jurisdiction, aiSpecificationComputeData, classification);
        aiSpecificationComputeData.setDisclosureText(disclosure);
        List<FeatureTreeBO> featureTree = claimFeatureContentBO.getFeatureTree();
        if (CollectionUtils.isNotEmpty(featureTree)) {
            List<ComputeClaimFeature> claimFeatures = featureTree.stream().map(this::convertToComputeClaimFeature)
                    .toList();
            aiSpecificationComputeData.setClaimFeatures(claimFeatures);
        }
        return aiSpecificationComputeData;
    }


    /**
     * 获取摘要时候的RD撰写服务的请求对象
     *
     * @param taskId 说明书撰写的任务Id
     * @return 请求compute服务的对象
     */
    public AiSpecificationComputeData getAbstComputeReqData(String taskId) {

        AiSpecificationComputeData aiSpecificationComputeData = claimParseManager.parseSimpleFormattedClmByTaskId(
                taskId);
        if (aiSpecificationComputeData == null) {
            throw new BizException(ContentErrorCodeEnum.CONTENT_EMPTY);
        }
        SpecificationTechWrapperBO specificationTechWrapperBO = aiTaskManager.getTaskContent(taskId,
                AiTaskContentTypeEnum.TECH_WRAPPER);
        String techField = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_TECH_FIELD);
        String summary = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SUMMARY);
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(taskId,
                AiTaskContentTypeEnum.INITIALIZATION);
        String category = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.CATEGORY);
        aiSpecificationComputeData.setDrawingNarrative(aiTaskManager.getTaskContent(taskId, FIGURES_PRE_INFO));
        aiSpecificationComputeData.setDisclosureText(initializationBO.getDisclosure());
        aiSpecificationComputeData.setPatentType(category);
        aiSpecificationComputeData.setDisclosureProblem(specificationTechWrapperBO.getTechnicalProblem());
        aiSpecificationComputeData.setDisclosureEfficacy(specificationTechWrapperBO.getBenefit());
        aiSpecificationComputeData.setDisclosureSolution(specificationTechWrapperBO.getTechnicalMethods());
        aiSpecificationComputeData.setTechField(techField);
        aiSpecificationComputeData.setSummary(summary);
        aiSpecificationComputeData.setJurisdiction(initializationBO.getJurisdiction());
        aiSpecificationComputeData.setPatentOffice(initializationBO.getJurisdiction().toLowerCase());
        
        // 查新和设置交底书实施例数据(用户选中的)，传递给算法接口，参与报告生成
        SpecificationDisclosureExtractEmbodimentBO disclosureEmbodimentBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_EXTRACT);
        if (Objects.nonNull(disclosureEmbodimentBO) && CollectionUtils.isNotEmpty(disclosureEmbodimentBO.getEmbodiments())) {
            List<DisclosureEmbodimentItem> disclosureEmbodiments = disclosureEmbodimentBO.getEmbodiments().stream().filter(DisclosureEmbodimentItem::getSelected).toList();
            if (CollectionUtils.isNotEmpty(disclosureEmbodiments)) {
                // 内部的number字段还需要设置一下,循环遍历设置，就取集合下标即可
                for (int i = 0; i < disclosureEmbodiments.size(); i++) {
                    disclosureEmbodiments.get(i).setNumber(i + 1);
                }
                aiSpecificationComputeData.setDisclosureEmbodiments(disclosureEmbodiments);
            }
        }

        return aiSpecificationComputeData;
    }

    private ComputeClaimFeature convertToComputeClaimFeature(FeatureTreeBO featureTreeBO) {
        ComputeClaimFeature computeClaimFeature = new ComputeClaimFeature();
        computeClaimFeature.setClaim(featureTreeBO.getNum());

        List<ComputeClaimFeature> detailList = convertToDetailList(featureTreeBO);
        computeClaimFeature.setClaimFeatures(detailList);

        return computeClaimFeature;
    }

    /**
     * 获取 embodimentKeywords的prompt
     *
     * @param embodimentKeyWordReqDTO embodimentKeyWordReqDTO
     * @return prompt
     */
    private String getEmbodimentKeywordsPrompt(EmbodimentKeyWordReqDTO embodimentKeyWordReqDTO) {
        String taskId = embodimentKeyWordReqDTO.getTaskId();
        String num = embodimentKeyWordReqDTO.getNum();
        String concept = embodimentKeyWordReqDTO.getConcept();
        ClaimFeatureContentBO claimFeatureContentBO = aiTaskManager.getTaskContent(taskId, FEATURE_TREE);
        List<FeatureTreeBO> featureTreeList = claimFeatureContentBO.getFeatureTree();
        if (CollectionUtils.isEmpty(featureTreeList)) {
            throw new BizException(ContentErrorCodeEnum.CONTENT_TYPE_INVALID);
        }
        Map<String, Map<String, FeatureTreeSimpleBO>> numToFeatureMap = buildFeatureMap(featureTreeList);
        Map<String, FeatureTreeSimpleBO> conceptToFeatureMap = numToFeatureMap.get(num);
        FeatureTreeSimpleBO featureTreeSimpleBO = conceptToFeatureMap.get(concept);
        SpecificationInitializationBO initializationBO = aiTaskManager.getTaskContent(taskId,
                AiTaskContentTypeEnum.INITIALIZATION);
        String category = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.CATEGORY);
        String jurisdiction = initializationBO.getJurisdiction();
        String lang = JurisdictionEnum.CNIPA.name().equals(jurisdiction) ? Constant.CN : Constant.EN;
        return openAiClient.buildPromptByPlatform(KEYWORD_EXPAND.getValue(),
                Map.of("featureTree", mapper.toJson(claimFeatureContentBO), "claimTYpe",
                        featureTreeSimpleBO.getClaimType().getDesc(lang), "category", category, "num", num, "feature",
                        featureTreeSimpleBO.getFeature(), "concept", concept), lang);
    }


    private List<ComputeClaimFeature> convertToDetailList(FeatureTreeBO featureTreeBO) {
        return IntStream.range(0, featureTreeBO.getClaimFeatures().size())
                .mapToObj(id -> createFeatureDetail(featureTreeBO.getClaimFeatures().get(id), id + 1)).toList();
    }

    private ComputeClaimFeature createFeatureDetail(ClaimFeature claimFeature, int id) {
        ComputeClaimFeature featureDetail = new ComputeClaimFeature();
        featureDetail.setId(id);
        featureDetail.setFeature(claimFeature.getFeature());
        featureDetail.setConcepts(claimFeature.getConcepts());
        return featureDetail;
    }


    private SpecificationInitializationBO getInitializationData(SpecificationContentReqDTO dto) {
        return aiTaskManager.getTaskContent(dto.getTaskId(), AiTaskContentTypeEnum.INITIALIZATION);
    }

    private ClaimParserResult parseClaims(SpecificationInitializationBO initializationBO) {
        return getClaimParserResult(initializationBO.getJurisdiction(), initializationBO.getClaim());
    }

    private HighlightParams createHighlightParams(String claimText) {
        HighlightParams params = new HighlightParams();
        params.setBlock(false);
        params.setText(claimText);
        params.setOffsets(Lists.newArrayList());
        return params;
    }

    private void processAllClaims(ClaimParserResult parserResult, List<HighlightOffset> offsets) {
        // 检查 parserResult 是否为 null
        if (parserResult == null || parserResult.getAllClaims() == null) {
            throw new BizException(ContentErrorCodeEnum.CONTENT_EMPTY);
        }

        for (ClaimParserDetail claim : parserResult.getAllClaims()) {
            offsets.add(createClaimHighlightOffset(claim));
            processClaimSegments(claim.getSegments(), offsets);
        }
    }

    private HighlightOffset createClaimHighlightOffset(ClaimParserDetail claim) {
        HighlightOffset offset = new HighlightOffset();
        offset.setStart(claim.getIdxStart());
        offset.setEnd(claim.getIdxEnd());
        offset.setTag(Tag.SPAN);
        offset.setText(StringUtils.EMPTY);
        String cssClass = getCssClassForClaim(claim.getParentNums());
        offset.setCssClass(cssClass);
        offset.setTagAttrs(createTagAttributes(claim.getNum(), claim.getParentNums()));

        return offset;
    }

    private String getCssClassForClaim(List<Integer> parentNums) {
        return CollectionUtils.isNotEmpty(parentNums) ? DEP_CLM_CSS_CLASS : INDEP_CLM_CSS_CLASS;
    }

    private Map<String, String> createTagAttributes(int claimNum, List<Integer> parentNums) {
        if (CollectionUtils.isNotEmpty(parentNums)) {
            return Map.of("parent", StringUtils.join(parentNums, ","), "num", String.valueOf(claimNum));
        }
        return Map.of("num", String.valueOf(claimNum));
    }

    private void processClaimSegments(ClaimSegments segments, List<HighlightOffset> offsets) {
        if (segments == null) {
            return;
        }
        ClaimParserIdx refinement = segments.getRefinement();
        if (refinement != null) {
            offsets.add(createRefinementOffset(refinement));
        }
    }

    private HighlightOffset createRefinementOffset(ClaimParserIdx refinement) {
        HighlightOffset offset = new HighlightOffset();
        offset.setStart(refinement.getIdxStart());
        offset.setEnd(refinement.getIdxEnd());
        offset.setTag(Tag.SPAN);
        offset.setText(StringUtils.EMPTY);
        offset.setCssClass(CLM_REFI_CSS_CLASS);
        return offset;
    }

    /**
     * 获取格式化去权力要求的坐标
     *
     * @param jurisdiction 受理局
     * @param inputClaim   输入文本
     * @return 权利要求格式化数据
     */
    private ClaimParserResult getClaimParserResult(String jurisdiction, String inputClaim) {
        String lang;
        if (JurisdictionEnum.CNIPA.name().equals(jurisdiction)) {
            lang = Constant.CN.toLowerCase();
        } else {
            lang = Constant.EN.toLowerCase();
        }
        ClaimParserRequest claimParserRequest = new ClaimParserRequest();
        claimParserRequest.setLang(lang);
        claimParserRequest.setText(inputClaim);
        //获取坐标
        return restTemplate.postForObject(claimParserServiceUrl, claimParserRequest, ClaimParserResult.class);
    }


    private Map<String, Map<String, FeatureTreeSimpleBO>> buildFeatureMap(List<FeatureTreeBO> featureTreeList) {
        Map<String, Map<String, FeatureTreeSimpleBO>> resultMap = Maps.newHashMapWithExpectedSize(
                featureTreeList.size());
        // 遍历 featureTree
        for (FeatureTreeBO featureTree : featureTreeList) {
            FeatureTreeSimpleBO featureTreeSimpleBO = new FeatureTreeSimpleBO();
            String num = featureTree.getNum();
            featureTreeSimpleBO.setNum(num);
            ClaimTypeEnum claimType = getClaimType(featureTree);
            featureTreeSimpleBO.setClaimType(claimType);
            Map<String, FeatureTreeSimpleBO> subMap = Maps.newHashMap();
            // 遍历 claimFeatures
            for (ClaimFeature claimFeature : featureTree.getClaimFeatures()) {
                List<String> concepts = claimFeature.getConcepts();
                String feature = claimFeature.getFeature();
                // 将 feature 添加到对应的 concepts 中
                for (String concept : concepts) {
                    subMap.computeIfAbsent(concept, k -> featureTreeSimpleBO).setFeature(feature);
                }
                resultMap.put(num, subMap);
            }
        }
        return resultMap;
    }


    private ClaimTypeEnum getClaimType(FeatureTreeBO featureTreeBO) {
        return StringUtils.isEmpty(featureTreeBO.getParent()) ? ClaimTypeEnum.INDEP_CLAIM : ClaimTypeEnum.DEP_CLAIM;
    }

    /**
     * 设置分类号相关数据
     *
     * @param jurisdiction               受理局，决定取什么分类号
     * @param aiSpecificationComputeData 调用compute服务的数据对象
     * @param classification             分类号列表
     */
    private void setClassificationInfo(String jurisdiction, AiSpecificationComputeData aiSpecificationComputeData,
            List<String> classification) {
        if (JurisdictionEnum.CNIPA.name().equals(jurisdiction)) {
            aiSpecificationComputeData.setPredictIpcs(classification);
            //ipc desc
            aiSpecificationComputeData.setPredictIpcDescriptions(
                    classificationManager.getClassificationDescriptions(Constant.CLASSIFICATION_TYPE_IPC,
                            classification, Constant.CN));
        } else {
            aiSpecificationComputeData.setPredictCpcs(classification);
            //cpc desc
            aiSpecificationComputeData.setPredictIpcDescriptions(
                    classificationManager.getClassificationDescriptions(Constant.CLASSIFICATION_TYPE_CPC,
                            classification, Constant.EN));
        }
    }

    /**
     * 上传多张图片到任务内容表
     */
    public ImageUploadResponseDTO uploadMultipleImages(String taskId, MultipartFile[] files, ImageUploadRequestDTO request) {
        aiTaskManager.checkEditPermission(taskId);
        ImageUploadResponseDTO imageUploadResponseDTO = imageUploadManager.uploadMultipleImages(files, request);
        // 保存图片信息到 task content 表
        if (CollectionUtils.isNotEmpty(imageUploadResponseDTO.getUploadedFiles())) {
            List<Figure> figureBOListList = getFigureBOs(imageUploadResponseDTO);
            saveFiguresToTaskContent(taskId, figureBOListList);
        }
        return imageUploadResponseDTO;
    }

    /**
     * 保存附图信息到任务内容表
     */
    private void saveFiguresToTaskContent(String taskId, List<Figure> figureBOListList) {
        FigureContentBO figureContentBO = aiTaskManager.getTaskContent(taskId, FIGURE_CONFIG);
        Optional.ofNullable(figureContentBO).ifPresentOrElse(
                existingContent -> {
                    Optional.ofNullable(existingContent.getFigures()).orElseGet(ArrayList::new).addAll(figureBOListList);
                    aiTaskManager.updateTaskContent(taskId, FIGURE_CONFIG, existingContent);
                },
                () -> {
                    FigureContentBO newFigureContentBO = new FigureContentBO();
                    newFigureContentBO.setFigures(figureBOListList);
                    aiTaskManager.updateTaskContent(taskId, FIGURE_CONFIG, newFigureContentBO);
                }
        );
    }

    private static @NotNull List<Figure> getFigureBOs(ImageUploadResponseDTO imageUploadResponseDTO) {
        List<Figure> figureBOList = new ArrayList<>();
        List<ImageUploadResponseDTO.UploadedFileInfo> uploadedFiles = imageUploadResponseDTO.getUploadedFiles();
        for (ImageUploadResponseDTO.UploadedFileInfo uploadedFile : uploadedFiles) {
            Figure figureBO = new Figure();
            Figure.Image image = new Figure.Image().setS3Key(uploadedFile.getS3Key());
            figureBO.setUniqueId(uploadedFile.getUniqueId());
            figureBO.setImage(image);
            figureBOList.add(figureBO);
        }
        return figureBOList;
    }

    public FigureContentBO getFigureInfo(String taskId) {
        FigureContentBO figureContentBO = aiTaskManager.getTaskContent(taskId, FIGURE_CONFIG);
        List<Figure> figureBOS = Optional.ofNullable(figureContentBO).map(FigureContentBO::getFigures)
                .orElseGet(ArrayList::new);
        for (Figure figureBO : figureBOS) {
            if (figureBO.getImage() != null && StringUtils.isNotBlank(figureBO.getImage().getS3Key())) {
                String signedUrl = fileManager.signFile(figureBO.getImage().getS3Key());
                figureBO.getImage().setUrl(signedUrl);
            }
        }
        return FigureContentBO.builder()
                .figures(figureBOS)
                .build();
    }

    /**
     * 获取术语信息
     * 优先返回已存在的术语信息，如果不存在则从权利要求特征树中提取术语
     *
     * @param taskId 任务ID
     * @return 术语内容信息
     */
    public TermContentBO getTermInfo(String taskId) {
        // 获取术语、特征树和附图内容
        Map<AiTaskContentTypeEnum, Object> type2ContentMap = aiTaskManager.getTaskContent(taskId, List.of(TERMS, FEATURE_TREE, FIGURE_CONFIG));

        // 检查是否已存在术语信息
        TermContentBO existingTermContent = (TermContentBO) type2ContentMap.get(TERMS);
        if (hasValidTerms(existingTermContent)) {
            log.info("术语信息已存在，任务ID: {}", taskId);
            // 更新标注状态
            updateTermAnnotationStatus(existingTermContent, (FigureContentBO) type2ContentMap.get(FIGURE_CONFIG));
            return existingTermContent;
        }

        // 从权利要求特征树中提取术语信息
        TermContentBO extractedTermContent = extractTermsFromFeatureTree(type2ContentMap);
        if (extractedTermContent != null) {
            log.info("从特征树中提取术语信息，任务ID: {}, 术语数量: {}", taskId, extractedTermContent.getTerms().size());
            // 保存提取的术语信息到任务内容
            aiTaskManager.updateTaskContent(taskId, TERMS, extractedTermContent);
            // 更新标注状态
            updateTermAnnotationStatus(extractedTermContent, (FigureContentBO) type2ContentMap.get(FIGURE_CONFIG));
            return extractedTermContent;
        }

        // 返回空的术语内容
        log.warn("未找到术语信息，任务ID: {}", taskId);
        return createEmptyTermContent();
    }

    /**
     * 检查术语内容是否有效
     */
    private boolean hasValidTerms(TermContentBO termContentBO) {
        return termContentBO != null && CollectionUtils.isNotEmpty(termContentBO.getTerms());
    }

    /**
     * 从特征树中提取术语信息
     */
    private TermContentBO extractTermsFromFeatureTree(Map<AiTaskContentTypeEnum, Object> type2ContentMap) {
        ClaimFeatureContentBO claimFeatureContentBO = (ClaimFeatureContentBO) type2ContentMap.get(FEATURE_TREE);

        if (claimFeatureContentBO == null || CollectionUtils.isEmpty(claimFeatureContentBO.getFeatureTree())) {
            return null;
        }

        // 提取所有概念名称并去重
        List<String> termNames = claimFeatureContentBO.getFeatureTree().stream()
                .flatMap(featureTree -> featureTree.getClaimFeatures().stream())
                .flatMap(claimFeature -> claimFeature.getConcepts().stream())
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(termNames)) {
            return null;
        }

        // 构建术语列表，ID为自然数字符串，从1开始
        List<Term> terms = new ArrayList<>();
        for (int i = 0; i < termNames.size(); i++) {
            String id = String.valueOf(i + 1);
            terms.add(createTermFromName(termNames.get(i), id));
        }

        return TermContentBO.builder()
                .terms(terms)
                .build();
    }

    /**
     * 根据名称和ID创建术语对象
     * @param name 术语名称
     * @param id   唯一ID（自然数字符串）
     */
    private Term createTermFromName(String name, String id) {
        return Term.builder()
                .uniqueId(id)
                .name(name)
                .annotated(false)
                .build();
    }

    /**
     * 创建空的术语内容
     */
    private TermContentBO createEmptyTermContent() {
        return TermContentBO.builder()
                .terms(new ArrayList<>())
                .build();
    }

    /**
     * 更新术语的标注状态
     * 根据所有标注的图中是否包含该术语来设置annotated属性
     *
     * @param termContentBO 术语内容对象
     * @param figureContentBO 附图内容对象
     */
    private void updateTermAnnotationStatus(TermContentBO termContentBO, FigureContentBO figureContentBO) {
        if (termContentBO == null || CollectionUtils.isEmpty(termContentBO.getTerms())) {
            log.info("术语内容为空或术语列表为空，无法更新标注状态");
            return;
        }

        // 收集所有附图中标注的术语唯一标识
        Set<String> annotatedTermIds = collectAnnotatedTermIds(figureContentBO);

        // 更新每个术语的标注状态
        for (Term term : termContentBO.getTerms()) {
            if (term != null && StringUtils.isNotBlank(term.getUniqueId())) {
                boolean isAnnotated = annotatedTermIds.contains(term.getUniqueId());
                term.setAnnotated(isAnnotated);
                log.debug("术语 {} (ID: {}) 标注状态: {}", term.getName(), term.getUniqueId(), isAnnotated);
            }
        }

        log.info("术语标注状态更新完成，总术语数: {}, 已标注术语数: {}",
                termContentBO.getTerms().size(), annotatedTermIds.size());
    }

    /**
     * 收集所有附图中标注的术语唯一标识
     *
     * @param figureContentBO 附图内容对象
     * @return 已标注的术语唯一标识集合
     */
    private Set<String> collectAnnotatedTermIds(FigureContentBO figureContentBO) {
        Set<String> annotatedTermIds = new HashSet<>();

        if (figureContentBO == null || CollectionUtils.isEmpty(figureContentBO.getFigures())) {
            log.debug("附图内容为空，无标注信息");
            return annotatedTermIds;
        }

        for (Figure figure : figureContentBO.getFigures()) {
            if (figure != null && CollectionUtils.isNotEmpty(figure.getAnnotations())) {
                for (Figure.AnnotationsBean annotation : figure.getAnnotations()) {
                    if (annotation != null && StringUtils.isNotBlank(annotation.getTermUniqueId())) {
                        annotatedTermIds.add(annotation.getTermUniqueId());
                        log.debug("发现标注术语ID: {}", annotation.getTermUniqueId());
                    }
                }
            }
        }

        log.debug("收集到已标注术语ID数量: {}", annotatedTermIds.size());
        return annotatedTermIds;
    }

    /**
     * 获取算法处理状态
     *
     * @param taskId 任务ID
     * @return 各种内容类型的算法处理状态
     */
    public Map<String, String> getAlgorithmStatus(String taskId) {
        return algorithmAsyncProcessor.getAlgorithmStatus(taskId);
    }


    public void saveSingleEmbodiment(SingleEmbodimentModifyReqDTO reqDTO) {
        String taskId = reqDTO.getTaskId();
        aiTaskManager.checkEditPermission(taskId);
        SpecificationEmbodimentGenerateBO embodimentGenerateBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_CONTENT);
        if (embodimentGenerateBO == null) {
            embodimentGenerateBO = new SpecificationEmbodimentGenerateBO();
            embodimentGenerateBO.setEmbodimentGenerateItems(new ArrayList<>());
        }
        List<EmbodimentGenerateItem> embodimentGenerateItems = embodimentGenerateBO.getEmbodimentGenerateItems();
        if(CollectionUtils.isEmpty(embodimentGenerateItems)) {
            embodimentGenerateItems = new ArrayList<>();
            EmbodimentGenerateItem item = new EmbodimentGenerateItem();
            item.setEmbodimentNumber(reqDTO.getEmbodimentNumber());
            item.setType(reqDTO.getType());
            item.setClaimNumbers(reqDTO.getClaimNumbers());
            item.setText(reqDTO.getText());
            embodimentGenerateItems.add(item);
        } else {
            // 查找是否存在相同的实施例编号
            Optional<EmbodimentGenerateItem> existingItemOpt = embodimentGenerateItems.stream()
                    .filter(item -> Objects.equals(item.getEmbodimentNumber(), reqDTO.getEmbodimentNumber()))
                    .findFirst();
            if (existingItemOpt.isPresent()) {
                // 更新已存在的实施例
                EmbodimentGenerateItem existingItem = existingItemOpt.get();
                existingItem.setType(reqDTO.getType());
                existingItem.setClaimNumbers(reqDTO.getClaimNumbers());
                existingItem.setText(reqDTO.getText());
            } else {
                // 添加新的实施例
                EmbodimentGenerateItem newItem = new EmbodimentGenerateItem();
                newItem.setEmbodimentNumber(reqDTO.getEmbodimentNumber());
                newItem.setType(reqDTO.getType());
                newItem.setClaimNumbers(reqDTO.getClaimNumbers());
                newItem.setText(reqDTO.getText());
                embodimentGenerateItems.add(newItem);
            }
        }

        // embodimentGenerateItems按照embodimentNumber排序
        embodimentGenerateItems.sort(Comparator.comparing(EmbodimentGenerateItem::getEmbodimentNumber));
        embodimentGenerateBO.setEmbodimentGenerateItems(embodimentGenerateItems);
        aiTaskManager.updateTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_CONTENT, mapper.toJson(embodimentGenerateBO));
    }

    public void deleteSingleEmbodiment(SingleEmbodimentModifyReqDTO reqDTO) {
        String taskId = reqDTO.getTaskId();
        aiTaskManager.checkEditPermission(taskId);
        SpecificationEmbodimentGenerateBO embodimentGenerateBO = aiTaskManager.getTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_CONTENT);
        if (embodimentGenerateBO == null) {
            return;
        }
        List<EmbodimentGenerateItem> embodimentGenerateItems = embodimentGenerateBO.getEmbodimentGenerateItems();
        if (CollectionUtils.isEmpty(embodimentGenerateItems)) {
            return;
        } else {
            // 过滤掉要删除的实施例
            embodimentGenerateItems = embodimentGenerateItems.stream().filter(item -> !Objects.equals(item.getEmbodimentNumber(), reqDTO.getEmbodimentNumber())).collect(Collectors.toList());
        }

        // embodimentGenerateItems按照embodimentNumber排序
        if (CollectionUtils.isNotEmpty(embodimentGenerateItems)) {
            embodimentGenerateItems.sort(Comparator.comparing(EmbodimentGenerateItem::getEmbodimentNumber));
            embodimentGenerateBO.setEmbodimentGenerateItems(embodimentGenerateItems);
        } else {
            embodimentGenerateBO.setEmbodimentGenerateItems(new ArrayList<>());
        }
        aiTaskManager.updateTaskContent(taskId, AiTaskContentTypeEnum.SPECIFICATION_EMBODIMENT_CONTENT, mapper.toJson(embodimentGenerateBO));
    }

    /**
     * 将解析出的图片结果保存到任务内容表中，content_type=FIGURES
     *
     * @param taskId 任务ID
     * @return 保存的图片内容信息
     */
    public FigureContentBO saveParsedFigures(String taskId) {
        // 检查编辑权限
        aiTaskManager.checkEditPermission(taskId);

        // 获取文档解析的图片结果
        DocumentParseFigureBO documentParseFigureBO = aiTaskManager.getTaskContent(taskId, DOCUMENT_PARSE_FIGURES);
        if (documentParseFigureBO == null || CollectionUtils.isEmpty(documentParseFigureBO.getImageS3Keys())) {
            log.warn("未找到解析的图片数据，任务ID: {}", taskId);
            throw new BizException(ContentErrorCodeEnum.CONTENT_NOT_FOUND);
        }

        log.info("开始保存解析图片结果，任务ID: {}, 图片数量: {}", taskId, documentParseFigureBO.getImageS3Keys().size());

        // 转换为Figure列表
        List<Figure> figures = convertS3KeysToFigures(documentParseFigureBO.getImageS3Keys());

        // 构建FigureContentBO
        FigureContentBO figureContentBO = FigureContentBO.builder()
                .figures(figures)
                .build();

        // 保存到任务内容表，content_type为FIGURES
        aiTaskManager.updateTaskContent(taskId, FIGURES, figureContentBO);

        log.info("解析图片结果保存成功，任务ID: {}, 图片数量: {}", taskId, figures.size());

        return figureContentBO;
    }

    /**
     * 将S3键列表转换为Figure列表
     *
     * @param imageS3Keys 图片S3键列表
     * @return Figure列表
     */
    private List<Figure> convertS3KeysToFigures(List<String> imageS3Keys) {
        List<Figure> figures = new ArrayList<>();

        for (int i = 0; i < imageS3Keys.size(); i++) {
            String s3Key = imageS3Keys.get(i);

            // 生成图片ID和名称
            String figureId = "parsed_figure_" + (i + 1);
            String figureName = extractFileNameFromS3Key(s3Key);

            // 生成签名URL
            String signedUrl = fileManager.signFile(s3Key);

            // 创建Image对象
            Figure.Image image = new Figure.Image()
                    .setS3Key(s3Key)
                    .setUrl(signedUrl);

            // 创建Figure对象
            Figure figure = new Figure()
                    .setId(figureId)
                    .setName(figureName)
                    .setImage(image)
                    .setAnnotations(new ArrayList<>());

            figures.add(figure);
        }

        return figures;
    }

    /**
     * 从S3键中提取文件名
     *
     * @param s3Key S3存储键
     * @return 文件名
     */
    private String extractFileNameFromS3Key(String s3Key) {
        if (StringUtils.isBlank(s3Key)) {
            return "unknown";
        }

        // 从S3键中提取文件名（取最后一个/后的部分）
        int lastSlashIndex = s3Key.lastIndexOf('/');
        if (lastSlashIndex >= 0 && lastSlashIndex < s3Key.length() - 1) {
            return s3Key.substring(lastSlashIndex + 1);
        }

        return s3Key;
    }

}
